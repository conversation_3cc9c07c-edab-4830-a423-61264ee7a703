import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Customer } from '@modules/customer/customer.entity'
import { Repository } from 'typeorm'
import { Location } from '@modules/location/location.entity'
import {
  CreateLocationDTO,
  GetLocationsDTO,
  UpdateLocationDTO,
} from '@modules/location/dto/location.dto'
import { CustomerService } from '@modules/customer/customer.service'
import { MSG } from '../../utils/messages/common'
import { pagingResponse } from '../../utils/functions/pagination'

@Injectable()
export class LocationService {
  constructor(
    @InjectRepository(Location)
    public locationRepo: Repository<Location>,
    private readonly customerService: CustomerService,
  ) {}

  async createLocation(payload: CreateLocationDTO): Promise<Location> {
    const { blockNo, postalCode, customerId, ...rest } = payload

    const findCustomer = await this.customerService.customerRepo.findOne({
      where: { id: customerId },
    })

    if (!findCustomer) {
      throw new NotFoundException(MSG.CUSTOMER_NOT_FOUND)
    }

    return this.locationRepo.save({
      ...rest,
      postalCode,
      blockNo,
      customer: findCustomer,
    })
  }

  async getLocations(
    {
      keyword,
      pageIndex,
      pageSize,
      postalCodeSort,
      streetSort,
      buildingSort,
      createdAtSort,
    }: GetLocationsDTO,
    customerId: Customer['id'],
  ) {
    const findCustomer = await this.customerService.customerRepo.findOne({
      where: { id: customerId },
    })

    if (!findCustomer) {
      throw new NotFoundException(MSG.CUSTOMER_NOT_FOUND)
    }

    const queryBuilder = this.locationRepo
      .createQueryBuilder('location')
      .leftJoinAndSelect('location.customer', 'customer')
      .where('customer.id = :customerId', { customerId: findCustomer.id })
      .andWhere(
        '(location.blockNo ILIKE :keyword OR location.street ILIKE :keyword)',
        { keyword: `%${keyword}%` },
      )

    // Sorting logic
    if (postalCodeSort) {
      queryBuilder.addOrderBy('location.postalCode', postalCodeSort)
    } else if (streetSort) {
      queryBuilder.addOrderBy('location.street', streetSort)
    } else if (buildingSort) {
      queryBuilder.addOrderBy('location.building', buildingSort)
    } else if (createdAtSort) {
      queryBuilder.addOrderBy('location.createdAt', createdAtSort)
    } else {
      queryBuilder.addOrderBy('location.createdAt', 'DESC')
    }

    const [data, total] = await queryBuilder
      .skip((pageIndex - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount()

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async getLocation(locationId: Location['id']): Promise<Location> {
    return this.locationRepo.findOne({
      where: { id: locationId },
    })
  }

  async updateLocation(
    payload: UpdateLocationDTO,
    locationId: Location['id'],
  ): Promise<Location> {
    const findLocation = await this.locationRepo.findOne({
      where: { id: locationId },
      relations: ['customer'],
    })
    if (!findLocation) {
      throw new NotFoundException(MSG.LOCATION_NOT_FOUND)
    }
    const { blockNo, postalCode, ...rest } = payload

    return this.locationRepo.save({
      ...findLocation,
      ...rest,
      id: locationId,
      postalCode,
      blockNo,
    })
  }

  async deleteLocation(locationId: Location['id']) {
    const findLocation = await this.locationRepo.findOne({
      where: { id: locationId },
    })
    if (!findLocation) {
      throw new NotFoundException(MSG.LOCATION_NOT_FOUND)
    }

    return this.locationRepo.update(locationId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
